import { Component, ViewChild } from '@angular/core';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { SidebarComponent } from './sidebar.component';
import { HeaderComponent } from './header.component';
import { MatSidenav } from '@angular/material/sidenav';
import { BusinessService } from '../services/business.service';

@Component({
  selector: 'app-layout',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatSidenavModule,
    MatToolbarModule,
    MatIconModule,
    MatButtonModule,
    MatTooltipModule,
    SidebarComponent,
    HeaderComponent
  ],
  template: `
    <div class="app-shell" [class.sidebar-collapsed]="collapsed && !isMobile" [class.mobile-layout]="isMobile">
      <!-- Mobile Overlay Backdrop -->
      <div class="mobile-backdrop"
           *ngIf="isMobile && mobileMenuOpen"
           (click)="closeMobileMenu()"></div>

      <!-- Sidebar -->
      <aside class="app-sidebar"
             [class.collapsed]="collapsed && !isMobile"
             [class.mobile-open]="isMobile && mobileMenuOpen">



        <!-- Sidebar Content -->
        <app-sidebar
          [collapsed]="collapsed && !isMobile"
          (businessSelectionChange)="onBusinessSelectionChange($event)"
          (toggleSidebar)="toggleSidebar()">
        </app-sidebar>
      </aside>

      <!-- Main Content Area -->
      <div class="app-main">
        <!-- Header -->
        <header class="app-header">
          <app-header
            [showMenuButton]="isMobile"
            (menuToggle)="toggleMobileMenu()">
          </app-header>
        </header>

        <!-- Content -->
        <main class="app-content">
          <router-outlet></router-outlet>
        </main>
      </div>
    </div>
  `,
  styleUrls: ['./layout.component.scss']
})
export class LayoutComponent {
  collapsed = false;
  isMobile = false;
  mobileMenuOpen = false;
  selectedBusinessIds: string[] = ['1'];

  constructor(private businessService: BusinessService) {
    if (typeof window !== 'undefined' && window.localStorage) {
      this.collapsed = localStorage.getItem('sidebarCollapsed') === 'true';
    }
    this.updateResponsive();
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', this.updateResponsive.bind(this));
    }
  }

  updateResponsive() {
    this.isMobile = typeof window !== 'undefined' ? window.innerWidth < 900 : false;
    if (this.isMobile) {
      this.collapsed = false;
    }
  }

  toggleSidebar() {
    this.collapsed = !this.collapsed;
    if (typeof window !== 'undefined' && window.localStorage) {
      localStorage.setItem('sidebarCollapsed', String(this.collapsed));
    }
  }

  toggleMobileMenu() {
    this.mobileMenuOpen = !this.mobileMenuOpen;
  }

  closeMobileMenu() {
    this.mobileMenuOpen = false;
  }

  onBusinessSelectionChange(businessIds: string[]) {
    this.selectedBusinessIds = businessIds;
    // Update business service with new selection
    this.businessService.setSelectedBusinessIds(businessIds);
    console.log('Business selection changed:', businessIds);
  }
}