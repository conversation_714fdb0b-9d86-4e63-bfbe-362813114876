import { Component, Input, Output, EventEmitter } from '@angular/core';
import { MatListModule } from '@angular/material/list';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { MatDividerModule } from '@angular/material/divider';
import { MatSelectModule } from '@angular/material/select';
import { MatOptionModule } from '@angular/material/core';
import { MatBadgeModule } from '@angular/material/badge';
import { MatTooltipModule } from '@angular/material/tooltip';
import { BusinessSelectorComponent, Business } from './business-selector/business-selector.component';
import { UserIconComponent } from './user-menu/user-menu.component';
import { StaffManagerThemeService } from '../core/theme/staffmanager-theme';

@Component({
  selector: 'app-sidebar',
  standalone: true,
  imports: [CommonModule, RouterModule, MatListModule, MatIconModule, MatButtonModule, MatDividerModule, MatSelectModule, MatOptionModule, MatBadgeModule, MatTooltipModule, BusinessSelectorComponent, UserIconComponent],
  host: {
    '[class.collapsed]': 'collapsed'
  },
  template: `
    <nav class="sidebar-nav"
         [class.collapsed]="collapsed"
         [class.dark-theme]="themeService.isDark()"
         aria-label="Main navigation">
      <!-- Hamburger Toggle Button - Above Business Selector -->
      <div class="sidebar-header">
        <div class="sidebar-toggle-container">
          <button mat-icon-button
                  class="hamburger-toggle"
                  [class.collapsed]="collapsed"
                  (click)="onToggleSidebar()"
                  [attr.aria-label]="collapsed ? 'Expand sidebar' : 'Collapse sidebar'"
                  [matTooltip]="collapsed ? 'Expand sidebar' : 'Collapse sidebar'"
                  matTooltipPosition="right"
                  matTooltipClass="sidebar-tooltip">
            <div class="hamburger-icon" style="display: flex !important; flex-direction: column; justify-content: space-between; width: 20px; height: 16px;">
              <span class="hamburger-line" style="background: white !important; width: 100% !important; height: 3px !important; display: block !important; margin: 0 !important; border-radius: 1px;"></span>
              <span class="hamburger-line" style="background: white !important; width: 100% !important; height: 3px !important; display: block !important; margin: 0 !important; border-radius: 1px;"></span>
              <span class="hamburger-line" style="background: white !important; width: 100% !important; height: 3px !important; display: block !important; margin: 0 !important; border-radius: 1px;"></span>
            </div>
          </button>
        </div>
      </div>

      <!-- Business Selector Section -->
      <div class="sidebar-business-section">
        <app-business-selector
          [businesses]="businesses"
          [selectedBusinessIds]="selectedBusinessIds"
          [collapsed]="collapsed"
          (selectionChange)="onBusinessSelectionChange($event)"
        ></app-business-selector>
      </div>

      <mat-divider class="sidebar-divider"></mat-divider>
      <mat-nav-list>
        <div class="sidebar-section-label" *ngIf="!collapsed">Main</div>
        <a mat-list-item routerLink="/dashboard" routerLinkActive="active"
           [matTooltip]="collapsed ? 'Dashboard' : ''"
           matTooltipPosition="right"
           matTooltipClass="sidebar-tooltip"
           aria-label="Dashboard">
          <mat-icon [style.font-size]="collapsed ? '48px' : '24px'"
                    [style.width]="collapsed ? '48px' : '24px'"
                    [style.height]="collapsed ? '48px' : '24px'"
                    [style.line-height]="collapsed ? '48px' : '24px'"
                    [style.display]="'flex'"
                    [style.align-items]="'center'"
                    [style.justify-content]="'center'"
                    [style.margin]="collapsed ? '0 auto' : '0 16px 0 0'"
                    style="transition: all 0.3s ease;">dashboard</mat-icon>
          <span *ngIf="!collapsed">Dashboard</span>
        </a>
        <a mat-list-item routerLink="/calendar" routerLinkActive="active"
           [matTooltip]="collapsed ? 'Calendar' : ''"
           matTooltipPosition="right"
           matTooltipClass="sidebar-tooltip"
           aria-label="Calendar">
          <mat-icon [style.font-size]="collapsed ? '48px' : '24px'"
                    [style.width]="collapsed ? '48px' : '24px'"
                    [style.height]="collapsed ? '48px' : '24px'"
                    [style.line-height]="collapsed ? '48px' : '24px'"
                    [style.display]="'flex'"
                    [style.align-items]="'center'"
                    [style.justify-content]="'center'"
                    [style.margin]="collapsed ? '0 auto' : '0 16px 0 0'"
                    style="transition: all 0.3s ease;">calendar_month</mat-icon>
          <span *ngIf="!collapsed">Calendar</span>
        </a>
        <div class="sidebar-collapsible">
          <a mat-list-item (click)="toggleStaffMenu()" [class.active]="staffMenuOpen"
             [matTooltip]="collapsed ? 'Staff' : ''"
             matTooltipPosition="right"
             matTooltipClass="sidebar-tooltip"
             aria-label="Staff">
            <mat-icon [style.font-size]="collapsed ? '48px' : '24px'"
                      [style.width]="collapsed ? '48px' : '24px'"
                      [style.height]="collapsed ? '48px' : '24px'"
                      [style.line-height]="collapsed ? '48px' : '24px'"
                      [style.display]="'flex'"
                      [style.align-items]="'center'"
                      [style.justify-content]="'center'"
                      [style.margin]="collapsed ? '0 auto' : '0 16px 0 0'"
                      style="transition: all 0.3s ease;">people</mat-icon>
            <span *ngIf="!collapsed">Staff</span>
            <mat-icon class="sidebar-expand-icon" *ngIf="!collapsed">{{ staffMenuOpen ? 'expand_less' : 'expand_more' }}</mat-icon>
          </a>
          <div class="sidebar-submenu" *ngIf="staffMenuOpen && !collapsed">
            <a mat-list-item routerLink="/staff" routerLinkActive="active" class="sidebar-subitem" aria-label="Staff Directory">
              <mat-icon>list</mat-icon>
              <span>Directory</span>
            </a>
            <a mat-list-item routerLink="/staff/new" routerLinkActive="active" class="sidebar-subitem" aria-label="Add Staff">
              <mat-icon>person_add</mat-icon>
              <span>Add Staff</span>
            </a>
          </div>
        </div>
        <a mat-list-item routerLink="/tasks" routerLinkActive="active"
           [matTooltip]="collapsed ? (taskBadgeCount > 0 ? 'Tasks (' + taskBadgeCount + ')' : 'Tasks') : ''"
           matTooltipPosition="right"
           matTooltipClass="sidebar-tooltip"
           [attr.aria-label]="taskBadgeCount > 0 ? taskBadgeCount + ' pending tasks' : 'Tasks'">
          <mat-icon [matBadge]="(!collapsed && taskBadgeCount > 0) ? taskBadgeCount : null"
            matBadgeColor="warn"
            [matBadgeHidden]="collapsed || taskBadgeCount === 0"
            aria-hidden="false"
            [style.font-size]="collapsed ? '48px' : '24px'"
            [style.width]="collapsed ? '48px' : '24px'"
            [style.height]="collapsed ? '48px' : '24px'"
            [style.line-height]="collapsed ? '48px' : '24px'"
            [style.display]="'flex'"
            [style.align-items]="'center'"
            [style.justify-content]="'center'"
            [style.margin]="collapsed ? '0 auto' : '0 16px 0 0'"
            style="transition: all 0.3s ease;"
          >assignment_turned_in</mat-icon>
          <span *ngIf="!collapsed">Tasks</span>
        </a>
        <a mat-list-item routerLink="/settings" routerLinkActive="active"
           [matTooltip]="collapsed ? 'Settings' : ''"
           matTooltipPosition="right"
           matTooltipClass="sidebar-tooltip"
           aria-label="Settings">
          <mat-icon [style.font-size]="collapsed ? '48px' : '24px'"
                    [style.width]="collapsed ? '48px' : '24px'"
                    [style.height]="collapsed ? '48px' : '24px'"
                    [style.line-height]="collapsed ? '48px' : '24px'"
                    [style.display]="'flex'"
                    [style.align-items]="'center'"
                    [style.justify-content]="'center'"
                    [style.margin]="collapsed ? '0 auto' : '0 16px 0 0'"
                    style="transition: all 0.3s ease;">settings</mat-icon>
          <span *ngIf="!collapsed">Settings</span>
        </a>
      </mat-nav-list>

      <!-- User Icon at Bottom -->
      <app-user-icon [collapsed]="collapsed"></app-user-icon>
    </nav>
  `,
  styleUrls: ['./sidebar.component.scss']
})
export class SidebarComponent {
  @Input() collapsed = false;

  @Output() businessSelectionChange = new EventEmitter<string[]>();
  @Output() toggleSidebar = new EventEmitter<void>();

  staffMenuOpen = false;
  selectedBusinessIds: string[] = ['1'];
  businesses: Business[] = [
    { id: '1', name: 'S&E Jewelers', logoUrl: '' },
    { id: '2', name: 'Diamond District', logoUrl: '' },
    { id: '3', name: 'Gold Rush', logoUrl: '' },
    { id: '4', name: 'Precious Gems Co.', logoUrl: '' }
  ];
  taskBadgeCount = 5;

  constructor(public themeService: StaffManagerThemeService) {}

  toggleStaffMenu() {
    this.staffMenuOpen = !this.staffMenuOpen;
  }

  onToggleSidebar() {
    console.log('🔥 HAMBURGER CLICKED - TOGGLE SIDEBAR');
    this.toggleSidebar.emit();
  }

  onBusinessSelectionChange(ids: string[]) {
    this.selectedBusinessIds = ids;
    this.businessSelectionChange.emit(ids);
  }
}