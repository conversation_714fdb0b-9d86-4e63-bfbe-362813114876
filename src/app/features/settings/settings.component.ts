import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { StaffManagerThemeService } from '../../core/theme/staffmanager-theme';

@Component({
  selector: 'app-settings',
  standalone: true,
  imports: [CommonModule, MatCardModule, MatButtonModule, MatIconModule, MatSlideToggleModule],
  template: `
    <div class="settings-container">
      <mat-card>
        <mat-card-header>
          <mat-card-title>
            <mat-icon>settings</mat-icon>
            Settings
          </mat-card-title>
          <mat-card-subtitle>Configure your StaffManager preferences</mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <div class="setting-item">
            <label>Dark Mode</label>
            <mat-slide-toggle 
              [checked]="themeService.isDark()"
              (change)="themeService.toggleTheme()">
            </mat-slide-toggle>
          </div>
          
          <p>Additional settings coming soon:</p>
          <ul>
            <li>Notification preferences</li>
            <li>Business configuration</li>
            <li>User management</li>
            <li>Integration settings</li>
          </ul>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .settings-container {
      padding: 24px;
    }
    mat-card-title {
      display: flex;
      align-items: center;
      gap: 8px;
    }
    .setting-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 0;
      border-bottom: 1px solid #eee;
      margin-bottom: 16px;
    }
  `]
})
export class SettingsComponent {
  constructor(public themeService: StaffManagerThemeService) {}
}
