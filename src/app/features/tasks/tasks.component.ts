import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-tasks',
  standalone: true,
  imports: [CommonModule, MatCardModule, MatButtonModule, MatIconModule],
  template: `
    <div class="tasks-container">
      <mat-card>
        <mat-card-header>
          <mat-card-title>
            <mat-icon>assignment</mat-icon>
            Task Management
          </mat-card-title>
          <mat-card-subtitle>Manage tasks and assignments</mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <p>Task management feature coming soon! This will include:</p>
          <ul>
            <li>Task creation and assignment</li>
            <li>Progress tracking</li>
            <li>Due date management</li>
            <li>Team collaboration</li>
          </ul>
        </mat-card-content>
        <mat-card-actions>
          <button mat-raised-button color="primary">
            <mat-icon>add</mat-icon>
            Create Task
          </button>
        </mat-card-actions>
      </mat-card>
    </div>
  `,
  styles: [`
    .tasks-container {
      padding: 24px;
    }
    mat-card-title {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  `]
})
export class TasksComponent {}
