import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-calendar',
  standalone: true,
  imports: [CommonModule, MatCardModule, MatButtonModule, MatIconModule],
  template: `
    <div class="calendar-container">
      <mat-card>
        <mat-card-header>
          <mat-card-title>
            <mat-icon>calendar_today</mat-icon>
            Calendar & Scheduling
          </mat-card-title>
          <mat-card-subtitle>Manage staff schedules and shifts</mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <p>Calendar feature coming soon! This will include:</p>
          <ul>
            <li>Staff scheduling</li>
            <li>Shift management</li>
            <li>Time-off requests</li>
            <li>Calendar integrations</li>
          </ul>
        </mat-card-content>
        <mat-card-actions>
          <button mat-raised-button color="primary">
            <mat-icon>add</mat-icon>
            Add Shift
          </button>
        </mat-card-actions>
      </mat-card>
    </div>
  `,
  styles: [`
    .calendar-container {
      padding: 24px;
    }
    mat-card-title {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  `]
})
export class CalendarComponent {}
